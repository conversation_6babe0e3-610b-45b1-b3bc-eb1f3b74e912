#!/bin/bash

# CSS 构建脚本
# 用于开发和生产环境的 Tailwind CSS 构建

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 检查是否安装了 Node.js 依赖
if [ ! -d "node_modules" ]; then
    echo_info "安装 Node.js 依赖..."
    npm install
    check_status "依赖安装成功" "依赖安装失败"
fi

# 根据参数决定构建模式
if [ "$1" = "dev" ] || [ "$1" = "watch" ]; then
    echo_info "启动开发模式 CSS 构建（监听文件变化）..."
    npm run build-css
elif [ "$1" = "prod" ] || [ "$1" = "production" ]; then
    echo_info "构建生产环境 CSS..."
    npm run build-css-prod
    check_status "生产环境 CSS 构建完成" "CSS 构建失败"
else
    echo_info "构建开发环境 CSS..."
    npx tailwindcss -i ./static/css/input.css -o ./static/css/output.css
    check_status "开发环境 CSS 构建完成" "CSS 构建失败"
fi

echo_success "=== CSS 构建完成! ==="
