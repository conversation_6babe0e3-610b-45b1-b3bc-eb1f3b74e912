@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .btn-success {
    @apply bg-success-500 hover:bg-success-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .btn-danger {
    @apply bg-danger-500 hover:bg-danger-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg p-6 shadow-card hover:shadow-card-hover transition-all duration-200;
  }
  
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center;
  }
  
  .modal-content {
    @apply bg-white rounded-lg p-6 max-w-2xl w-full mx-4 shadow-modal;
  }

  .tag-item {
    @apply inline-flex items-center px-5 py-2 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 no-underline transition-all duration-300 border border-gray-300 shadow-sm font-medium relative overflow-hidden;
  }

  .tag-item:hover {
    @apply from-gray-200 to-gray-300 -translate-y-0.5 shadow-md;
  }

  .tag-item.active {
    @apply from-primary-500 to-primary-600 text-white border-primary-700 shadow-lg font-semibold;
  }

  .tag-count {
    @apply ml-2 px-2 py-1 bg-black bg-opacity-10 rounded-xl text-xs font-semibold;
  }

  .tag-item.active .tag-count {
    @apply bg-white bg-opacity-25;
  }
}

/* 保留必要的自定义样式 */
@layer utilities {
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 移动端特定样式 */
  @media (max-width: 768px) {
    .mobile-nav-hidden {
      display: none;
    }

    .mobile-scroll-x {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .mobile-full-width {
      width: 100% !important;
      text-align: center;
    }
  }
}
