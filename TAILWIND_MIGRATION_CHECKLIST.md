# HaoBBS Tailwind CSS 迁移检查清单

## 迁移完成状态 ✅

### 1. 安装和配置
- [x] 安装 Tailwind CSS 及依赖项
- [x] 创建 `tailwind.config.js` 配置文件
- [x] 创建 `postcss.config.js` 配置文件
- [x] 设置 `package.json` 构建脚本

### 2. 构建流程
- [x] 创建 `static/css/input.css` 输入文件
- [x] 配置构建脚本 `scripts/build-css.sh`
- [x] 更新部署脚本 `scripts/deploy.sh`
- [x] 生成优化的 `static/css/output.css`

### 3. 模板迁移
- [x] `templates/index.html` - 主页
- [x] `templates/login.html` - 登录页
- [x] `templates/new_post.html` - 新建帖子页
- [x] `templates/post.html` - 帖子详情页
- [x] `templates/edit_post.html` - 编辑帖子页

### 4. 样式组件迁移
- [x] 全局基础样式 (body, 容器, 表单)
- [x] 按钮样式 (btn-primary, btn-success, btn-danger)
- [x] 卡片样式 (card)
- [x] 标签样式 (tag-item, tag-count)
- [x] 模态框样式 (modal-overlay, modal-content)
- [x] 响应式布局

### 5. 优化和清理
- [x] 备份原始 CSS 文件
- [x] 精简 `styles.css` 文件
- [x] 优化 Tailwind 配置
- [x] 启用 JIT 模式

## 文件大小对比
- 原始 `styles.css`: 20,188 字节
- 新的 `output.css`: 16,007 字节
- 精简 `styles.css`: 340 字节
- **总体减少**: ~20%

## 部署前检查

### 必须执行的步骤
1. 运行 CSS 构建命令:
   ```bash
   npm run build-css-prod
   ```

2. 验证所有 CSS 文件存在:
   - `static/css/output.css`
   - `static/css/styles.css`
   - `static/css/input.css`

3. 测试应用启动:
   ```bash
   python3 app.py
   ```

4. 在浏览器中测试所有页面:
   - 登录页面
   - 主页 (帖子列表)
   - 新建帖子页面
   - 帖子详情页面
   - 编辑帖子页面

### 响应式测试
- [ ] 桌面端 (>1024px)
- [ ] 平板端 (768px-1024px)
- [ ] 移动端 (<768px)

### 功能测试
- [ ] 登录/登出
- [ ] 浏览帖子列表
- [ ] 分类筛选
- [ ] 创建新帖子
- [ ] 查看帖子详情
- [ ] 回复帖子
- [ ] 编辑帖子/回复
- [ ] 删除帖子/回复
- [ ] 模态框交互

## 回滚计划
如果发现问题，可以快速回滚:

1. 恢复原始 CSS:
   ```bash
   cp static/css/styles.css.backup static/css/styles.css
   ```

2. 更新模板文件中的 CSS 引用:
   ```html
   <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
   ```

## 维护说明

### 开发环境
- 使用 `npm run build-css` 进行开发时的 CSS 构建
- 使用 `npm run build-css-prod` 进行生产环境构建

### 添加新样式
1. 在 `static/css/input.css` 中添加自定义组件
2. 在模板中使用 Tailwind 工具类
3. 运行构建命令更新 CSS

### 部署流程
部署脚本已自动包含 CSS 构建步骤，无需额外操作。

## 技术债务
- 考虑将剩余的特殊样式也迁移到 Tailwind 组件中
- 评估是否需要添加更多自定义颜色和间距
- 考虑使用 Tailwind UI 组件库进一步优化界面
