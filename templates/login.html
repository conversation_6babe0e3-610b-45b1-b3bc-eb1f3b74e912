<!DOCTYPE html>
<html>
<head>
    <title>登录</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
</head>
<body class="font-sans m-0 p-0 leading-relaxed">
    <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <div class="flex flex-row justify-between items-center mb-4 py-2">
            <h1 class="m-0 text-primary-500 text-2xl md:text-3xl">用户登录</h1>
        </div>

        <form action="{{ url_for('login') }}" method="post" class="bg-white p-6 rounded-lg shadow-card mt-3 max-w-2xl mx-auto md:p-8">
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="mb-3">
                <label for="username" class="block mb-1 text-gray-700 font-medium text-sm">用户名</label>
                <input type="text" id="username" name="username" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="mb-3">
                <label for="password" class="block mb-1 text-gray-700 font-medium text-sm">密码</label>
                <input type="password" id="password" name="password" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="flex justify-end gap-2 mt-6">
                <button type="submit" class="btn-success">登录</button>
            </div>
        </form>
    </div>
</body>
</html>